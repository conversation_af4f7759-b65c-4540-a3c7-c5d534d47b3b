Feature: Draw Intersection

  Background:
    Given The user is on File Screen
    When The user enters "E2E-Draw-Intersection.mp4" into the search bar
    Then The displayed file results should contain "E2E-Draw-Intersection.mp4"
    When The user selects the "file" named "E2E-Draw-Intersection.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully

  @e2e @draw-intersection
  Scenario: Verify UI
    And The following information should be visible on the page:
      | Field            | Value                     |
      | File Heading     | E2E-Draw-Intersection.mp4 |
      | Event Breadcrumb | April 050                 |

  @e2e @draw-intersection
  Scenario: Verify user can draw a region box on media player