import { When, Before, Then, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { fileDetailsPage } from '../../../pages/fileDetailsPage';
import {
  AccordionName,
  DetailsPanelButton,
  PageButton,
  PopoverButton,
  TabName,
  VideoControl,
} from '../../../support/helperFunction/fileDetailHelper';
import '../activeTab/activeTab.step';
import '../event-screen/event-screen.step';
import '../main-page/main-page.steps';
import '../file-screen/file-screen.step';

Before({ tags: '@file-details' }, () => {
  cy.LoginLandingPage();
});

When('The user clicks the row with file name {string} and status {string}', (fileName: string, status: string) => {
  fileDetailsPage.clickFileRow(fileName, status);
});

When('The user clicks the {string} button on the details panel', (buttonName: DetailsPanelButton) => {
  fileDetailsPage.clickButtonOnDetailsPanel(buttonName);
});

Then('The File Viewer page should be displayed successfully', () => {
  fileDetailsPage.verifyFileViewerPage();
  // eslint-disable-next-line cypress/no-unnecessary-waiting
  cy.wait(10000);
});

Then('The following information should be visible on the page:', (dataTable: DataTable) => {
  fileDetailsPage.verifyInformation(dataTable);
});

Then('The user see the notification message {string}', (message: string) => {
  fileDetailsPage.verifyNotificationMessage(message);
});

When('The user clicks on the {string} detection thumbnail', (ordinal: string) => {
  fileDetailsPage.clickDetectionThumbnail(ordinal);
});

Then('The following UI elements should be updated in the details panel:', (dataTable: DataTable) => {
  fileDetailsPage.verifyDetailsPanelUpdate(dataTable);
});

Then('the video should jump to the detection time and be paused', () => {
  fileDetailsPage.verifyVideoJumpAndPause();
});

When('The user clicks the {string} accordion', (accordionName: AccordionName) => {
  fileDetailsPage.clickAccordion(accordionName);
});

Then('The {string} accordion should be {string}', (accordionName: AccordionName, state: 'expanded' | 'collapsed') => {
  fileDetailsPage.verifyAccordionState(accordionName, state);
});

When('The user clicks the "Find Matches" button', () => {
  fileDetailsPage.clickFindMatchesButton();
});

When('The user creates a new match group named {string}', (groupName: string) => {
  fileDetailsPage.createNewMatchGroup(groupName);
});

When('The user clicks the {string} button in the popover', (buttonName: PopoverButton) => {
  fileDetailsPage.clickPopoverButton(buttonName);
});

When('The user selects the existing match group {string}', (groupName: string) => {
  fileDetailsPage.selectExistingMatchGroup(groupName);
});

When('The user clicks the {string} tab', (tabName: TabName) => {
  fileDetailsPage.clickTab(tabName);
});

When('The user opens the menu for the {string} group', (groupName: string) => {
  fileDetailsPage.openGroupMenu(groupName);
});

When('The user selects the {string} option from the menu', (menuOption: string) => {
  fileDetailsPage.selectMenuOption(menuOption);
});

When('The user confirms the deletion', () => {
  fileDetailsPage.confirmDeletion();
});

When('The user clicks the {string} video control', (_controlName: VideoControl) => {
  fileDetailsPage.clickVideoControl();
});

Then('The video should be {string}', (state: 'playing' | 'paused') => {
  fileDetailsPage.verifyVideoState(state);
});

When('The user sets the video playback speed to {string}', (speed: string) => {
  fileDetailsPage.setPlaybackSpeed(speed);
});

Then('The video playback speed should be {float}', (expectedSpeed: number) => {
  fileDetailsPage.verifyPlaybackSpeed(expectedSpeed);
});

When('The user opens the thumbnail scale slider', () => {
  fileDetailsPage.openThumbnailScaleSlider();
});

Then('The thumbnail scale should display {string}', (expectedScale: string) => {
  fileDetailsPage.verifyThumbnailScale(expectedScale);
});

When('The user filters detections by attribute {string} and value {string}', (attribute: string, value: string) => {
  fileDetailsPage.filterDetections(attribute, value);
});

Then('The number of visible thumbnails should be {int}', (expectedCount: number) => {
  fileDetailsPage.verifyVisibleThumbnails(expectedCount);
});

Then('The following file metadata should be displayed:', (dataTable: DataTable) => {
  fileDetailsPage.verifyFileMetadata(dataTable);
});

Then('The results per page should be {string}', (count: string) => {
  fileDetailsPage.verifyResultsPerPage(count);
});

When('The user changes the results per page to the following values:', (dataTable: DataTable) => {
  fileDetailsPage.changeResultsPerPage(dataTable);
});

When('The user clicks the {string} page button', (direction: PageButton) => {
  fileDetailsPage.clickPageButton(direction);
});

Then('the current page number should be {int}', (pageNumber: number) => {
  fileDetailsPage.verifyCurrentPageNumber(pageNumber);
});
