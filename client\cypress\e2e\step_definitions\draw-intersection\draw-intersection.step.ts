import { When, Before, Then, Given } from '@badeball/cypress-cucumber-preprocessor';
import '../activeTab/activeTab.step';
import '../event-screen/event-screen.step';
import '../main-page/main-page.steps';
import '../file-screen/file-screen.step';
import '../file-details/file-details.step';
import { fileDetailsPage } from '../../../pages/fileDetailsPage';
import { getIndexFromOrdinal } from '../../../support/helperFunction/activeTabHelper';

Before({ tags: '@draw-intersection' }, () => {
  cy.LoginLandingPage();
});

Given('The user searches for and opens file {string}', (fileName: string) => {
  fileDetailsPage.navigateToFileAndView(fileName);
});

When(
  'The user draws {string} region box at coordinates {int}, {int}, {int}, {int}',
  (regionNumber: string, x1: number, y1: number, x2: number, y2: number) => {
    const regionIndex = getIndexFromOrdinal(regionNumber);

    if (regionIndex === 0) {
      fileDetailsPage.verifyMediaPlayerDrawingEnabled();
      fileDetailsPage.drawRegionBox(x1, y1, x2, y2, regionIndex);
    } else {
      fileDetailsPage.verifyMediaPlayerDrawingEnabledSoft();
      fileDetailsPage.attemptDrawRegionBox(x1, y1, x2, y2, regionIndex);
    }
  }
);

Then('The region box should be visible on the media player', () => {
  fileDetailsPage.verifyRegionBoxVisible();
});

Then('Only one region box should be visible on the media player', () => {
  fileDetailsPage.verifyOnlyOneRegionBoxVisible();
});

When(
  'The user moves the region box from coordinates {int}, {int} to {int}, {int}',
  (fromX: number, fromY: number, toX: number, toY: number) => {
    fileDetailsPage.moveRegionBox(fromX, fromY, toX, toY);
  }
);

When('The user clicks on {string} button', (buttonName: string) => {
  if (buttonName === 'Reset Selection') {
    fileDetailsPage.clickResetSelectionButton();
  } else {
    throw new Error(`Unknown button: ${buttonName}`);
  }
});

Then('The region box should not be visible on the media player', () => {
  fileDetailsPage.verifyRegionBoxNotVisible();
});

When('The user right-clicks on the region box', () => {
  fileDetailsPage.rightClickRegionBox(150, 150);
});

When('The user clicks on {string} in the context menu', (menuOption: string) => {
  if (menuOption === 'Remove') {
    fileDetailsPage.clickRemoveInContextMenu();
  } else {
    throw new Error(`Unknown context menu option: ${menuOption}`);
  }
});
