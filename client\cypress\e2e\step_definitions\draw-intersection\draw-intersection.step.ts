import { When, Before, Then, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import '../activeTab/activeTab.step';
import '../event-screen/event-screen.step';
import '../main-page/main-page.steps';
import '../file-screen/file-screen.step';
import '../file-details/file-details.step';
import { fileDetailsPage } from '../../../pages/fileDetailsPage';

const getIndexFromOrdinal = (ordinal: string): number => {
  const numberPart = parseInt(ordinal, 10);
  if (isNaN(numberPart)) {
    throw new Error(`Could not parse number from ordinal string: ${ordinal}`);
  }
  return numberPart - 1;
};

Before({ tags: '@draw-intersection' }, () => {
  cy.LoginLandingPage();
});

When(
  'The user draws {string} region box at coordinates {int}, {int}, {int}, {int}',
  (regionNumber: string, x1: number, y1: number, x2: number, y2: number) => {
    const regionIndex = getIndexFromOrdinal(regionNumber);

    if (regionIndex === 0) {
      // First drawing - verify drawing is enabled
      fileDetailsPage.verifyMediaPlayerDrawingEnabled();
      fileDetailsPage.drawRegionBox(x1, y1, x2, y2, regionIndex);
    } else {
      // Subsequent drawings - soft check and attempt drawing (to test the limit)
      fileDetailsPage.verifyMediaPlayerDrawingEnabledSoft();
      fileDetailsPage.attemptDrawRegionBox(x1, y1, x2, y2, regionIndex);
    }
  }
);

Then('The region box should be visible on the media player', () => {
  fileDetailsPage.verifyRegionBoxVisible();
});

Then('Only one region box should be visible on the media player', () => {
  fileDetailsPage.verifyOnlyOneRegionBoxVisible();
});
