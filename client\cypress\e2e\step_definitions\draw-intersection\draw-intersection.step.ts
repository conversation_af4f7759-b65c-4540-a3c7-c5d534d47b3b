import { When, Before, Then, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import '../activeTab/activeTab.step';
import '../event-screen/event-screen.step';
import '../main-page/main-page.steps';
import '../file-screen/file-screen.step';
import '../file-details/file-details.step';
import { fileDetailsPage } from '../../../pages/fileDetailsPage';

const getIndexFromOrdinal = (ordinal: string): number => {
  const numberPart = parseInt(ordinal, 10);
  if (isNaN(numberPart)) {
    throw new Error(`Could not parse number from ordinal string: ${ordinal}`);
  }
  return numberPart - 1;
};

Before({ tags: '@draw-intersection' }, () => {
  cy.LoginLandingPage();
});

When(
  'The user draws {string} region box at coordinates {int}, {int}, {int}, {int}',
  (regionNumber: string, x1: number, y1: number, x2: number, y2: number) => {
    const regionIndex = getIndexFromOrdinal(regionNumber);

    // Check if drawing is enabled and conditionally draw
    cy.getDataIdCy({ idAlias: 'media-player' }).within(() => {
      cy.getDataIdCy({ idAlias: 'overlay-background' }).then(($overlay) => {
        const cursor = $overlay.css('cursor');
        cy.log(`Media player cursor style: ${cursor}`);

        if (cursor === 'crosshair') {
          cy.log('Drawing is enabled, proceeding with drawing');
          fileDetailsPage.drawRegionBox(x1, y1, x2, y2, regionIndex);
        } else {
          cy.log(`Drawing is not enabled (cursor: ${cursor}). Skipping drawing action.`);
        }
      });
    });
  }
);

Then('The region box should be visible on the media player', () => {
  cy.getDataIdCy({ idAlias: 'media-player' }).within(() => {
    cy.getDataIdCy({ idAlias: 'overlay-background' }).then(($overlay) => {
      const cursor = $overlay.css('cursor');

      if (cursor === 'crosshair') {
        cy.log('Drawing is enabled, verifying region box');
        fileDetailsPage.verifyRegionBoxVisible();
      } else {
        cy.log('Drawing is not enabled, skipping region box verification');
      }
    });
  });
});

Then('Only one region box should be visible on the media player', () => {
  cy.getDataIdCy({ idAlias: 'media-player' }).within(() => {
    cy.getDataIdCy({ idAlias: 'overlay-background' }).then(($overlay) => {
      const cursor = $overlay.css('cursor');

      if (cursor === 'crosshair') {
        cy.log('Drawing is enabled, verifying only one region box');
        fileDetailsPage.verifyOnlyOneRegionBoxVisible();
      } else {
        cy.log('Drawing is not enabled, skipping region box count verification');
      }
    });
  });
});
