import { When, Before, Then, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import '../activeTab/activeTab.step';
import '../event-screen/event-screen.step';
import '../main-page/main-page.steps';
import '../file-screen/file-screen.step';
import '../file-details/file-details.step';
import { fileDetailsPage } from '../../../pages/fileDetailsPage';

Before({ tags: '@draw-intersection' }, () => {
  cy.LoginLandingPage();
});

const ordinalToNumber = (ordinal: string): number | undefined => {
  const ordinalMap: { [key: string]: number } = {
    '1st': 1,
    '2nd': 2,
    '3rd': 3,
    '4th': 4,
    '5th': 5,
    '6th': 6,
    '7th': 7,
    '8th': 8,
    '9th': 9,
    '10th': 10,
  };
  return ordinalMap[ordinal.toLowerCase()];
};

When(
  'The user draws {string} region box at coordinates {int}, {int}, {int}, {int}',
  (regionNumber: string, x1: number, y1: number, x2: number, y2: number) => {
    const orderNumber = ordinalToNumber(regionNumber);
    if (orderNumber === undefined) {
      throw new Error(`Invalid ordinal number: ${regionNumber}`);
    }

    cy.getDataIdCy({ idAlias: 'media-player' }).within(() => {
      cy.get('[data-testid="overlay-background"]').should('have.css', 'cursor', 'crosshair');
    });

    fileDetailsPage.drawRegionBox(x1, y1, x2, y2);
  }
);

Then('The region box should be visible on the media player', () => {
  fileDetailsPage.verifyRegionBoxVisible();
});
