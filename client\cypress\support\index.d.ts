declare namespace Cypress {
  interface UserInfo {
    body: {
      token: string;
      userId: string;
    };
  }
  interface Chainable {
    LoginToApp(): Chainable;
    LoginLandingPage(): Chainable<void>;
    Graphql<T = unknown>(
      query: string,
      variables?: unknown
    ): Chainable<Cypress.Response<T>>;
    waitMainPageIsLoaded(): Chainable<void>;
    waitFilesTabIsLoaded(): Chainable<void>;
    waitForElement(selector: string, timeout?: number): Chainable<JQuery<HTMLElement>>;
    waitForElementToDisappear(selector: string, timeout?: number): Chainable<JQuery<HTMLElement>>;
    getDataIdCy({
      idAlias,
      options,
    }: {
      idAlias: string;
      options?: Partial<
        Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow
      >;
    }): Cypress.Chainable<JQuery<HTMLElement>>;
    selectFromDropdown(selector: string, value: string): Chainable<JQuery<HTMLElement>>;
    repeat({
      action,
      times,
    }: {
      action: unknown;
      times: number;
    }): Chainable<void>;
    awaitNetworkResponseCode({
      alias,
      code,
      repeat,
    }: {
      alias: string;
      code: number;
      repeat?: number;
    }): Chainable<void>;
    assertTableColumnSorted(label: string, orderBy: string): Chainable<void>;
    SelectFile({ fileName }: { fileName: string }): Chainable<void>;
    interceptGraphQLQuery(query: string, alias: string): Chainable<void>;
    updateMatchGroup(
      eventId: string,
      matchGroupId: string,
      searchName: string,
      attribute: Record<string, { label: string; value: string; key: string }[]>
    ): Chainable;
    deleteMatchGroupSearch(
      eventId: string,
      matchGroupId: string,
      searchName: string
    ): Chainable;
    createMatchGroup(eventId: string, matchGroupName: string): Chainable;
    deleteMatchGroup(eventId: string, matchGroupName: string): Chainable;
    DrawRegionBox({ x1, y1, x2, y2 }: { x1: number; y1: number; x2: number; y2: number }): Chainable<void>;
  }
}
