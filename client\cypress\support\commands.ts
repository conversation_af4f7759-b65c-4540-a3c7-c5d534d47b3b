import { LandingPageTestIds } from "./helperFunction/activeTabHelper";
import { v4 as uuidv4 } from 'uuid';

Cypress.Commands.add('LoginToApp', () =>
  cy
    .request({
      method: 'POST',
      url: `${Cypress.env('apiRoot')}/v1/admin/login`,
      form: true,
      body: {
        userName: Cypress.env('username'),
        password: Cypress.env('password'),
      },
    })
    .then((userInfo) => {
      console.log('=====', userInfo.body);
      Cypress.env('token', userInfo.body.token);
      Cypress.env('userId', userInfo.body.userId);
      return userInfo;
    })
);

Cypress.Commands.add('LoginLandingPage', () => {
  const url = Cypress.env('visitUrl');
  cy.LoginToApp();
  cy.visit(url);
});

Cypress.Commands.add('Graphql', (query, variables = {}) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiRoot')}/v3/graphql`,
    headers: { Authorization: 'Bearer ' + Cypress.env('token') },
    body: {
      query,
      variables,
    },
  }).then((res) => cy.wrap(res));
});

Cypress.Commands.add('getDataIdCy', ({ idAlias, options = {} }: { idAlias: string; options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow> }) => {
  const matches = idAlias.replace(/@/, '').split(' > ');

  const tagName = matches[0];
  const childCombinators: string | string[] = matches.slice(1).join(' > ') ?? '';
  const withChildCombinators = childCombinators.length > 0 ? ` > ${childCombinators}` : '';

  return cy.get(`[data-testid="${tagName}"]${withChildCombinators}`, options);
});

Cypress.Commands.add('waitMainPageIsLoaded', () => {
  cy.get('nav[aria-label="breadcrumb"] a', { timeout: 60000 }).should('contain.text', 'All Events');
  cy.get('[data-testid="table"] > div > span').should('not.exist');
  cy.get('[data-testid="table"] [data-testid="event-row-name"]').should('have.length.greaterThan', 0).first().should('be.visible').and('not.be.empty');
});

Cypress.Commands.add('waitFilesTabIsLoaded', () => {
  cy.get('nav[aria-label="breadcrumb"] a').should('contain.text', 'All Events');
  cy.get('[data-testid^="file-row-cell-"][data-testid$="-0"]').should('have.length.greaterThan', 0).first().should('be.visible');
});

Cypress.Commands.add('waitForElement', (selector: string, timeout = 10000) => cy.get(selector, { timeout }).should('be.visible'));

Cypress.Commands.add('waitForElementToDisappear', (selector: string, timeout = 10000) => cy.get(selector, { timeout }).should('not.exist'));

Cypress.Commands.add('selectFromDropdown', (selector: string, value: string) => cy.get(selector).select(value));

Cypress.Commands.add('repeat', ({ action, times }: { action: unknown; times: number }) => {
  if (typeof action === 'function') {
    Array.from({ length: times }, () => action());
  }
});

Cypress.Commands.add('awaitNetworkResponseCode', ({ alias, code, repeat = 1 }: { alias: string; code: number; repeat?: number }) => {
  cy.repeat({
    action: cy.wait(`${alias}`).its('response.statusCode').should('eq', code),
    times: repeat,
  });
  // cy.assertNoLoading();
});

Cypress.Commands.add(
  'interceptGraphQLQuery',
  (query: string, alias: string) => {
    cy.intercept('POST', /v3\/graphql/, (req) => {
      if (req.body.query === query) {
        req.alias = alias;
      }
    });
  }
);

Cypress.Commands.add(
  'assertTableColumnSorted',
  (label: string, orderBy: string) => {
    const columnMap: { [key: string]: string } = {
      'Event Name': LandingPageTestIds.COLUMN_0,
      'Event Time': LandingPageTestIds.COLUMN_1,
      'File Name': LandingPageTestIds.COLUMN_0,
      'Upload Date': LandingPageTestIds.COLUMN_2,
    };

    cy.getDataIdCy({ idAlias: columnMap[label] }).then(($th) => {
      const columnIndex = $th.index();

      const isFileColumn = label === 'File Name' || label === 'Upload Date';
      const rowSelector = isFileColumn ? '[data-testid^="file-row-"]' : '[data-testid^="event-row-"]';

      cy.get(rowSelector).then(($rows) => {
        const texts: string[] = [];
        const sampleRows = $rows.slice(0, 3);

        Cypress._.each(sampleRows, ($row) => {
          const cellText = Cypress.$($row)
            .find(`[data-testid$="-${columnIndex}"] [role="cell"]`)
            .text()
            .trim();
          if (cellText) {
            texts.push(cellText);
          }
        });

        let sortedTexts: string[] = [...texts];

        if (label === 'Event Time' || label === 'Upload Date') {
          const dates: Date[] = texts.map((text) => new Date(text));
          const sortedDates = [...dates].sort((a, b) => a.getTime() - b.getTime());

          if (orderBy === 'z-a') {
            sortedDates.reverse();
          }

          sortedTexts = sortedDates.map((date) => {
            const month = date.getMonth() + 1;
            const day = date.getDate();
            const year = date.getFullYear();
            let hours = date.getHours();
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12;
            return `${month}/${day}/${year}, ${hours}:${minutes} ${ampm}`;
          });
        } else {
          sortedTexts.sort();
          if (orderBy === 'z-a') {
            sortedTexts.reverse();
          }
        }

        expect(texts).to.deep.equal(sortedTexts);
      });
    });
  }
);

Cypress.Commands.add('SelectFile', ({ fileName }: { fileName: string }) => {
  cy.fixture(fileName, 'binary', { timeout: 120000 })
    .then(Cypress.Blob.binaryStringToBlob)
    .then((fileContent) => {

      let finalFileContent: Blob = fileContent;
      if (fileName === 'lucy.mp4') {
        // TODO: Replace with actual video file for more realistic testing
        const largerContent = new Array(1024 * 1024).fill('a').join('');
        finalFileContent = new Blob([largerContent], { type: 'video/mp4' });
      }

      cy.get('input[data-testid="drop-input"]').attachFile({
        fileContent: finalFileContent,
        fileName: fileName,
        mimeType: 'video/mp4',
        encoding: 'utf8',
      });
    });
});

Cypress.Commands.add(
  'updateMatchGroup',
  (
    eventId: string,
    matchGroupId: string,
    searchName: string,
    attributes?: Record<string, { label: string; value: string; key: string }[]>
  ) => cy.request({
    method: 'GET',
    url: `${Cypress.env('trackerApi')}/match-groups/?eventId=${eventId}&pageSize=10000&currentPage=1`,
    headers: {
      Authorization: `Bearer ${Cypress.env('token')}`,
    },
  }).then((getResponse) => {
    expect(getResponse.status).to.eq(200);

    const matchGroup = getResponse.body.results.find(
      (mg: {id: string}) => mg.id === matchGroupId
    );
    const existingSearches = matchGroup?.searches ?? [];

    const newSearch = {
      id: uuidv4(),
      searchName,
      searchTime: new Date().toISOString(),
      attributes: attributes ?? {},
    };

    const body = {
      searches: [...existingSearches, newSearch],
    };
    cy.log(JSON.stringify(body));

    return cy.request({
      method: 'PATCH',
      url: `${Cypress.env('trackerApi')}/match-groups/${matchGroupId}`,
      headers: {
        Authorization: `Bearer ${Cypress.env('token')}`,
      },
      body: {
        searches: [...existingSearches, newSearch],
      },
    }).then((patchResponse) => {
      expect(patchResponse.status).to.eq(200);
      cy.log(`Match Group ${matchGroupId} updated successfully`);
      return cy.wrap(patchResponse.body);
    });
  })
);

Cypress.Commands.add(
  'deleteMatchGroupSearch',
  (
    eventId: string,
    matchGroupId: string,
    searchName: string
  ) =>
    cy
      .request({
        method: 'GET',
        url: `${Cypress.env('trackerApi')}/match-groups/?eventId=${eventId}&pageSize=10000&currentPage=1`,
        headers: {
          Authorization: `Bearer ${Cypress.env('token')}`,
        },
      })
      .then((getResponse) => {
        expect(getResponse.status).to.eq(200);

        const matchGroup = getResponse.body.results.find(
          (mg: { id: string }) => mg.id === matchGroupId
        );

        if (!matchGroup) {
          throw new Error(`MatchGroup ${matchGroupId} not found`);
        }

        const searches = matchGroup.searches ?? [];
        const searchesToDelete = searches.filter(
          (s: { id: string; searchName: string }) => s.searchName === searchName
        );

        if (searchesToDelete.length === 0) {
          cy.log(`ℹNo searches found with name "${searchName}"`);
          return;
        }

        cy.log(`Deleting ${searchesToDelete.length} searches with name "${searchName}"`);

        return cy.wrap(searchesToDelete).each((search: { id: string; searchName: string }) => {
          cy.log(`Deleting search ${search.id} (${search.searchName})`);
          cy.request({
            method: 'DELETE',
            url: `${Cypress.env('trackerApi')}/match-groups/${matchGroupId}/search/${search.id}`,
            headers: {
              Authorization: `Bearer ${Cypress.env('token')}`,
            },
          }).then((deleteResponse) => {
            expect(deleteResponse.status).to.eq(200);
            cy.log(`Deleted search ${search.id} (${search.searchName})`);
          });
        });
      })
);

Cypress.Commands.add(
  'createMatchGroup',
  (
    eventId: string,
    matchGroupName: string
  ) =>
    cy
      .request({
        method: 'POST',
        url: `${Cypress.env('trackerApi')}/match-groups/`,
        headers: {
          Authorization: `Bearer ${Cypress.env('token')}`,
        },
        body: {
          eventId,
          name: matchGroupName,
        },
      })
      .then((getResponse) => {
        expect(getResponse.status).to.eq(200);

      })
);

Cypress.Commands.add(
  'deleteMatchGroup',
  (eventId: string, matchGroupName: string) => cy
    .request({
      method: 'GET',
      url: `${Cypress.env('trackerApi')}/match-groups/?eventId=${eventId}&pageSize=10000&currentPage=1`,
      headers: {
        Authorization: `Bearer ${Cypress.env('token')}`,
      },
    })
    .then((getResponse) => {
      expect(getResponse.status).to.eq(200);

      const matchGroups = getResponse.body.results.filter(
        (mg: { id: string; name: string }) => mg.name === matchGroupName
      );

      if (matchGroups.length === 0) {
        cy.log(`ℹ No match groups found with name "${matchGroupName}"`);
        return;
      }

      cy.log(`Deleting ${matchGroups.length} match group(s) named "${matchGroupName}"`);

      return cy.wrap(matchGroups).each((mg: { id: string; name: string }) => {
        cy.log(`Deleting match group ${mg.id} (${mg.name})`);
        cy.request({
          method: 'DELETE',
          url: `${Cypress.env('trackerApi')}/match-groups/${mg.id}`,
          headers: {
            Authorization: `Bearer ${Cypress.env('token')}`,
          },
        }).then((deleteResponse) => {
          expect(deleteResponse.status).to.eq(200);
          cy.log(`Deleted match group ${mg.id} (${mg.name})`);
        });
      });
    })
);

Cypress.Commands.add(
  'DrawRegionBox',
  ({ x1, y1, x2, y2, regionIndex = 0 }: RegionBoxCoordinates) => {
    cy.log(`${regionIndex}`);
    cy.getDataIdCy({ idAlias: 'media-player' }).within(() => {
      cy.get('[data-testid="overlay-background"]').as('overlay');
      cy.get('@overlay').then(($overlay) => {
        const cursor = $overlay.css('cursor');
        if (cursor === 'crosshair') {
          cy.log('Drawing is enabled, proceeding with drawing');
        } else {
          cy.log(`Drawing cursor is ${cursor}, attempting drawing anyway to test limits`);
        }
      });
      cy.get('@overlay').trigger('mousedown', {
        x: x1,
        y: y1,
        eventConstructor: 'MouseEvent',
        force: true,
        which: 1,
        button: 0,
      });

      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(100);

      cy.get('@overlay').trigger('mousemove', {
        x: x2,
        y: y2,
        eventConstructor: 'MouseEvent',
        force: true,
        which: 1,
        button: 0,
      });

      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(100);

      cy.get('@overlay').trigger('mouseup', {
        x: x2,
        y: y2,
        eventConstructor: 'MouseEvent',
        force: true,
        which: 1,
        button: 0,
      });
    });

    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(500);

    cy.getDataIdCy({ idAlias: 'media-player' }).within(() => {
      const boundingSelectors = [
        '[data-testid*="bounding"]',
        '[class*="bounding"]',
        '[data-testid*="region"]',
        '[class*="region"]',
        '[data-testid*="box"]',
        '[class*="box"]',
        'rect',
        '.react-draggable',
        'svg rect',
        'div[style*="position: absolute"]',
        'svg',
        'canvas'
      ];
      cy.get(boundingSelectors.join(', ')).should('exist');
    });
  }
);

Cypress.Commands.add(
  'MoveRegionBox',
  ({ fromX, fromY, toX, toY }: MoveRegionBoxCoordinates) => {
    cy.getDataIdCy({ idAlias: 'media-player' }).within(() => {
      const boundingSelectors = [
        'svg rect[fill]',
        '.react-draggable',
        '[data-testid*="bounding"]',
        '[class*="bounding"]',
        '[data-testid*="region"]',
        '[class*="region"]'
      ];

      cy.get(boundingSelectors.join(', ')).first().as('regionBox');

      cy.get('@regionBox').trigger('mousedown', {
        x: fromX,
        y: fromY,
        eventConstructor: 'MouseEvent',
        force: true,
        which: 1,
        button: 0,
      });

      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(100);

      cy.get('@regionBox').trigger('mousemove', {
        x: toX,
        y: toY,
        eventConstructor: 'MouseEvent',
        force: true,
        which: 1,
        button: 0,
      });

      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(100);

      cy.get('@regionBox').trigger('mouseup', {
        x: toX,
        y: toY,
        eventConstructor: 'MouseEvent',
        force: true,
        which: 1,
        button: 0,
      });

      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(500);
    });
  }
);

Cypress.Commands.add(
  'RightClickRegionBox',
  ({ x, y }: RightClickCoordinates) => {
    cy.getDataIdCy({ idAlias: 'media-player' }).within(() => {
      const boundingSelectors = [
        'svg rect[fill]',
        '.react-draggable',
        '[data-testid*="bounding"]',
        '[class*="bounding"]',
        '[data-testid*="region"]',
        '[class*="region"]'
      ];

      cy.get(boundingSelectors.join(', ')).first().as('regionBox');

      cy.get('@regionBox').rightclick({
        x,
        y,
        force: true,
      });
    });
  }
);
