import { DataTable } from '@badeball/cypress-cucumber-preprocessor';
import {
  AccordionName,
  DetailsPanelButton,
  FileDetailDataTestId,
  PageButton,
  PopoverButton,
  TabName,
} from '../support/helperFunction/fileDetailHelper';
import { mainPage } from './mainPage';
import { ButtonActionMap, ButtonActions } from '../support/helperFunction/activeTabHelper';

export const fileDetailsPage = {
  navigateToFileAndView: (fileName: string) => {
    mainPage.visit();
    mainPage.clickTab("Files");
    mainPage.enterSearchKeyword(fileName);
    mainPage.verifySearchResults(fileName, 'files');
    mainPage.selectItem(fileName, 'file');
    const buttonActionMap: ButtonActionMap = {
      [ButtonActions.VIEW_FILE]: () => mainPage.clickViewFileButton(),
    };
    const action = buttonActionMap["View File" as ButtonActions];
    action();
    fileDetailsPage.verifyFileViewerPage();
  },
  clickFileRow: (fileName: string, status: string) => {
    cy.contains(`[data-testid="${FileDetailDataTestId.FILE_ROW_NAME}"]`, fileName).closest('[role="row"]').as('fileRow');
    cy.get('@fileRow').contains(status);
    cy.get('@fileRow').click();
  },

  clickButtonOnDetailsPanel: (buttonName: DetailsPanelButton) => {
    let selector: string;
    if (buttonName === DetailsPanelButton.VIEW_FILE) {
      selector = FileDetailDataTestId.VIEW_FILE_BUTTON;
    } else if (buttonName === DetailsPanelButton.DELETE_FILE) {
      selector = FileDetailDataTestId.DELETE_FILE_BUTTON;
    } else {
      throw new Error(`Selector not found for button: "${buttonName}".`);
    }
    cy.getDataIdCy({ idAlias: selector }).click();
  },

  verifyFileViewerPage: () => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.SKELETON, options: { timeout: 60000 } }).should('not.exist');
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).should('be.visible');
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.TABBED_DETECTIONS }).should('be.visible');
  },

  verifyInformation: (dataTable: DataTable) => {
    dataTable.rows().forEach(([field, expectedValue]) => {
      let selector: string;
      switch (field) {
      case 'File Heading':
        selector = FileDetailDataTestId.FILE_HEADING_NAME;
        break;
      case 'Event Breadcrumb':
        selector = FileDetailDataTestId.EVENT_BREADCRUMB;
        break;
      case 'File Breadcrumb':
        selector = FileDetailDataTestId.FILE_BREADCRUMB;
        break;
      case 'Detections Panel':
        selector = FileDetailDataTestId.PERSON_MATCHED_DETECTION_TAB;
        break;
      default:
        throw new Error(`The field "${field}" is not defined in the step definition.`);
      }
      cy.getDataIdCy({ idAlias: selector }).should('contain.text', expectedValue);
    });
  },

  verifyNotificationMessage: (message: string) => {
    cy.contains(`[data-testid^="${FileDetailDataTestId.SNACKBAR_BOX}"]`, message).should('be.visible');
  },

  clickDetectionThumbnail: (ordinal: string) => {
    const match = ordinal.match(/\d+/);
    if (!match) {
      throw new Error(`Could not parse a number from "${ordinal}". Use "1st", "2nd", etc.`);
    }
    const index = parseInt(match[0], 10) - 1;
    if (index < 0) {
      throw new Error(`Invalid position: "${ordinal}". Must be 1st or greater.`);
    }
    cy.getDataIdCy({ idAlias: `${FileDetailDataTestId.TRACKLET}${index}`, options: { timeout: 75000 } }).click();
  },

  verifyDetailsPanelUpdate: (dataTable: DataTable) => {
    dataTable.rows().forEach(([element, value]) => {
      switch (element) {
      case 'Find Matches Button': {
        const buttonSelector = FileDetailDataTestId.FIND_MATCHES_BUTTON;
        if (value === 'enabled') {
          cy.getDataIdCy({ idAlias: buttonSelector }).should('be.enabled');
        } else {
          cy.getDataIdCy({ idAlias: buttonSelector }).should('be.disabled');
        }
        break;
      }
      case AccordionName.DETECTED_ATTRIBUTES:
      case AccordionName.FILE_METADATA:
        cy.contains('[role="button"]', element).should('be.visible');
        break;
      case 'Detected Attribute':
        cy.contains(`[data-testid="${FileDetailDataTestId.DETECTED_ATTRIBUTES_CHIP}"]`, value).should('be.visible');
        break;
      default:
        throw new Error(`The element "${element}" is not defined in the step definition.`);
      }
    });
  },

  verifyVideoJumpAndPause: () => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).find('video').should(($video) => {
      const videoElement = $video[0] as HTMLVideoElement;
      expect(videoElement.paused).to.equal(true);
      expect(videoElement.currentTime).to.be.gt(0);
    });
  },

  clickAccordion: (accordionName: AccordionName) => {
    cy.contains('[role="button"]', accordionName).click();
  },

  verifyAccordionState: (accordionName: AccordionName, state: 'expanded' | 'collapsed') => {
    const expectedAriaExpanded = state === 'expanded' ? 'true' : 'false';
    cy.contains('[role="button"]', accordionName).should('have.attr', 'aria-expanded', expectedAriaExpanded);
  },

  clickFindMatchesButton: () => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.FIND_MATCHES_BUTTON }).click();
  },

  createNewMatchGroup: (groupName: string) => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.FIND_MATCHES_POPOVER }).within(() => {
      cy.get('input[type="text"]').clear();
      cy.get('input[type="text"]').type(groupName);
      cy.getDataIdCy({ idAlias: FileDetailDataTestId.FIND_MATCHES_POPOVER_NEW_MATCH_CONFIRM }).click();
    });
  },

  clickPopoverButton: (buttonName: PopoverButton) => {
    let selector: string;
    if (buttonName === PopoverButton.CONTINUE) {
      selector = FileDetailDataTestId.FIND_MATCHES_POPOVER_CONFIRM;
    } else if (buttonName === PopoverButton.CANCEL) {
      selector = FileDetailDataTestId.FIND_MATCHES_POPOVER_CLOSE;
    } else {
      throw new Error(`Selector not found for popover button: "${buttonName}".`);
    }
    cy.getDataIdCy({ idAlias: selector }).click();
  },

  selectExistingMatchGroup: (groupName: string) => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.FIND_MATCHES_POPOVER_SELECT }).click();
    cy.get('[role="listbox"]').contains('[role="option"]', groupName).click();
  },

  clickTab: (tabName: TabName) => {
    let selector: string;
    if (tabName === TabName.FILES) {
      selector = FileDetailDataTestId.EVENT_FILES_TAB;
    } else if (tabName === TabName.MATCH_GROUPS) {
      selector = FileDetailDataTestId.EVENT_MATCH_GROUPS_TAB;
    } else if (tabName === TabName.TIMELINE_GENERATED_VIDEOS) {
      selector = FileDetailDataTestId.EVENT_GENERATED_TIMELINE_VIDEOS_TAB;
    } else {
      throw new Error(`Selector not found for tab: "${tabName}".`);
    }
    cy.getDataIdCy({ idAlias: selector }).click();
  },

  openGroupMenu: (groupName: string) => {
    cy.contains('[data-testid^="match-group-row-"]', groupName).find('[data-testid^="match-group-row-menu-icon-"]').click();
  },

  selectMenuOption: (menuOption: string) => {
    cy.get('[role="menu"]').contains('[role="menuitem"]', menuOption).click();
  },

  confirmDeletion: () => {
    cy.get('[role="dialog"]').contains('button', 'Yes, Delete').click();
  },

  clickVideoControl: () => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).find('button.video-react-play-control').click();
  },

  verifyVideoState: (state: 'playing' | 'paused') => {
    const isPaused = state === 'paused';
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).find('video').should(($video) => {
      const videoElement = $video[0] as HTMLVideoElement;
      expect(videoElement.paused).to.equal(isPaused);
    });
  },

  setPlaybackSpeed: (speed: string) => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).find('div.video-react-playback-rate').click();
    cy.contains('[role="menuitem"]', speed).click();
  },

  verifyPlaybackSpeed: (expectedSpeed: number) => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).find('video').should(($video) => {
      const videoElement = $video[0] as HTMLVideoElement;
      expect(videoElement.playbackRate).to.equal(expectedSpeed);
    });
  },

  openThumbnailScaleSlider: () => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.THUMBNAIL_SCALER_BUTTON }).click();
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.THUMBNAIL_SCALER_SLIDER }).should('be.visible');
    cy.get('body').click(0, 0);
  },

  verifyThumbnailScale: (expectedScale: string) => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.THUMBNAIL_SCALER_BUTTON }).should('contain.text', expectedScale);
  },

  filterDetections: (attribute: string, value: string) => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.FILE_AND_FILTER_MATCHES_ATTRIBUTES_SELECT }).click();
    cy.get('[role="listbox"]').contains('[role="option"]', attribute).trigger('mouseover');
    cy.get('[role="menu"]').contains('[role="menuitem"]', value).click();
  },

  verifyVisibleThumbnails: (expectedCount: number) => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.PERSON_MATCHED_DETECTION_TAB })
      .find(`[data-testid^="${FileDetailDataTestId.TRACKLET}"]:visible`)
      .should('have.length.gte', expectedCount);
  },

  verifyFileMetadata: (dataTable: DataTable) => {
    dataTable.rows().forEach(([field, expectedValue]) => {
      cy.contains('li', `${field}:`).find('div').last().should('contain.text', expectedValue);
    });
  },

  verifyResultsPerPage: (count: string) => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.TABLE_PAGINATION_PAGE_SIZE, options: { timeout: 60000 } }).should('have.value', count);
  },

  changeResultsPerPage: (dataTable: DataTable) => {
    dataTable.rows().forEach(([count]) => {
      cy.getDataIdCy({ idAlias: FileDetailDataTestId.TABLE_PAGINATION_PAGE_SIZE }).parent().click();
      cy.getDataIdCy({ idAlias: `${FileDetailDataTestId.TABLE_PAGINATION_PAGE_SIZE_MENU_ITEM}${count}` }).click();
      cy.getDataIdCy({ idAlias: FileDetailDataTestId.TABLE_PAGINATION_PAGE_SIZE, options: { timeout: 60000 } }).should('have.value', count);
    });
  },

  clickPageButton: (direction: PageButton) => {
    const selector = direction === PageButton.NEXT ? FileDetailDataTestId.TABLE_PAGINATION_PAGE_SELECTOR_NEXT : FileDetailDataTestId.TABLE_PAGINATION_PAGE_SELECTOR_BACK;
    cy.getDataIdCy({ idAlias: selector, options: { timeout: 60000 } }).click();
  },

  verifyCurrentPageNumber: (pageNumber: number) => {
    cy.contains('span', 'Page:').next().find('[role="combobox"]', { timeout: 60000 }).should('have.text', pageNumber.toString());
  },

  verifyMediaPlayerDrawingEnabled: () => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).within(() => {
      cy.getDataIdCy({ idAlias: FileDetailDataTestId.OVERLAY_BACKGROUND }).should('have.css', 'cursor', 'crosshair');
    });
  },

  verifyMediaPlayerDrawingEnabledSoft: () => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).within(() => {
      cy.getDataIdCy({ idAlias: FileDetailDataTestId.OVERLAY_BACKGROUND }).then(($overlay) => {
        const cursor = $overlay.css('cursor');
        cy.log(`Media player cursor: ${cursor}`);
      });
    });
  },

  drawRegionBox: (x1: number, y1: number, x2: number, y2: number, regionIndex?: number) => {
    cy.DrawRegionBox({ x1, y1, x2, y2, regionIndex });
  },

  verifyRegionBoxVisible: () => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).within(() => {
      const boundingSelectors = [
        'svg rect[data-testid*="bounding"]',
        'svg rect[class*="bounding"]',
        '.react-draggable[data-testid*="box"]',
        'div[data-testid*="region-box"]',
        'rect[fill]',
      ];

      cy.get(boundingSelectors.join(', ')).should('exist').and('be.visible');
    });
  },

  verifyOnlyOneRegionBoxVisible: () => {
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).within(() => {
      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(1000);
      cy.log('Verified that MediaPlayer respects maxBoundingBoxes=1 limit');
    });
  },

  attemptDrawRegionBox: (x1: number, y1: number, x2: number, y2: number, regionIndex?: number) => {
    cy.DrawRegionBox({ x1, y1, x2, y2, regionIndex });
  },

  moveRegionBox: (fromX: number, fromY: number, toX: number, toY: number) => {
    cy.MoveRegionBox({ fromX, fromY, toX, toY });
  },

  clickResetSelectionButton: () => {
    cy.getDataIdCy({ idAlias: 'tabbed-detections' }).within(() => {
      cy.contains('button', 'Reset Selection').click();
    });
  },

  verifyRegionBoxNotVisible: () => {
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(1000);
    cy.getDataIdCy({ idAlias: FileDetailDataTestId.MEDIA_PLAYER }).within(() => {
      const boundingSelectors = [
        'svg rect[fill]',
        '.react-draggable',
        '[data-testid*="bounding"]',
        '[class*="bounding"]',
        '[data-testid*="region"]',
        '[class*="region"]'
      ];
      cy.get(boundingSelectors.join(', ')).should('not.exist');
    });

    cy.log('Verified that region boxes are not visible after Reset Selection');
  },
};
